import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { Allow, Permission, RequestContext, TransactionalConnection, Logger, PaymentService, Ctx, Order } from '@vendure/core';

@Resolver()
export class SezzleVerificationResolver {
  constructor(
    private connection: TransactionalConnection,
    private paymentService: PaymentService,
  ) {}

  @Mutation()
  @Allow(Permission.Owner)
  async verifySezzlePayment(
    @Ctx() ctx: RequestContext,
    @Args() args: { orderCode: string }
  ): Promise<{ success: boolean; message: string }> {
    try {
      Logger.info(`[Sezzle] Verifying payment for order: ${args.orderCode}`, 'SezzleVerificationResolver');

      // Find the order by code with necessary relations
      const order = await this.connection.getRepository(ctx, Order).findOne({
        where: { code: args.orderCode },
        relations: [
          'payments',
          'lines',
          'lines.productVariant',
          'customer',
          'shippingLines',
          'shippingLines.shippingMethod'
        ],
      });

      if (!order) {
        Logger.error(`[Sezzle] Order not found: ${args.orderCode}`, 'SezzleVerificationResolver');
        return { success: false, message: 'Order not found' };
      }

      // Find the Sezzle payment
      const sezzlePayment = order.payments?.find((p: any) => p.method === 'sezzle' && p.state === 'Authorized');

      if (!sezzlePayment) {
        Logger.error(`[Sezzle] No authorized Sezzle payment found for order: ${args.orderCode}`, 'SezzleVerificationResolver');
        return { success: false, message: 'No authorized Sezzle payment found' };
      }

      Logger.info(`[Sezzle] Found Sezzle payment ${sezzlePayment.id} for order ${args.orderCode}`, 'SezzleVerificationResolver');

      // Attempt to settle the payment (this will verify with Sezzle and update status)
      const settlementResult = await this.paymentService.settlePayment(ctx, sezzlePayment.id);


      // Check if settlement was successful
      // SettlePaymentErrorResult has 'success: false' and 'errorMessage'
      // Payment object (success) has properties like 'id', 'state', etc.
      if (settlementResult && typeof settlementResult === 'object' && 'success' in settlementResult) {
        if (settlementResult.success === false) {
          // This is a SettlePaymentErrorResult
          const errorMessage = (settlementResult as any).errorMessage || 'Payment settlement failed';
          Logger.error(`[Sezzle] Payment settlement failed for order ${args.orderCode}: ${errorMessage}`, 'SezzleVerificationResolver');
          return { success: false, message: errorMessage };
        } else {
          // This is a successful SettlePaymentResult
          Logger.info(`[Sezzle] Payment settled successfully for order ${args.orderCode}`, 'SezzleVerificationResolver');
          return { success: true, message: 'Payment verified and settled successfully' };
        }
      } else if (settlementResult && typeof settlementResult === 'object' && 'id' in settlementResult) {
        // This is a Payment object (successful settlement)
        Logger.info(`[Sezzle] Payment settled successfully for order ${args.orderCode}`, 'SezzleVerificationResolver');
        return { success: true, message: 'Payment verified and settled successfully' };
      } else {
        // Unknown result type
        Logger.error(`[Sezzle] Unknown settlement result type for order ${args.orderCode}`, 'SezzleVerificationResolver');
        return { success: false, message: 'Unknown settlement result' };
      }
    } catch (error: any) {
      Logger.error(`[Sezzle] Payment verification error for order ${args.orderCode}: ${error.message}`, 'SezzleVerificationResolver');
      return { success: false, message: `Verification failed: ${error.message}` };
    }
  }
}
